<template>
  <span :id="sectionName">
    <section>
      <app-grid-form context="inline">
          <div class="columns is-multiline">
            <div class="column is-12">
              <app-customer v-model="call.lSaleCustomerKey" :disabled="true">
                Customer
              </app-customer>
            </div>
          </div>
      </app-grid-form>
    </section>

    <app-accordian v-for="(payment, index) in paymentsProxy" :expand-on-mount="payment.expandOnMount" :key="payment.temporaryPaymentKey || payment.lPaymentKey">
      <div class="_thumbnail">
        <app-data-point label="Type">{{ getTypeValue('Value', payment.lPaymentTypeKey) }}</app-data-point>
        <app-data-point label="Amount">{{ payment.tcAmount | usd }}</app-data-point>
        <app-data-point label="Received">{{ payment.dReceived }}</app-data-point>
      </div>

      <template slot="controls">
        <RefundPayment
          :callId="call.lCallKey"
          :paymentId="payment.lPaymentKey"
          :amount="payment.tcAmount"
          @onRefund="readCall" />
        <app-button @click="reconcilePayment(payment)" :disabled="!canReconcile(payment)">
          Reconcile
        </app-button>
        <app-button @click="removePayment(index)" :disabled="!canDelete(payment)">
          <i class="far fa-trash-alt"></i>
        </app-button>
      </template>

      <template slot="body">
        <app-grid-form context="inline">
          <div class="columns is-multiline">
            <div class="column is-4">
              <app-select :class="payment.focusHandle" v-model="payment.lPaymentTypeKey" :options="activePaymentTypes" keyAlias="Key" valueAlias="Value" :required="true" :disabled="true">
                Type
              </app-select>
            </div>
            <div class="column is-4">
              <app-number v-model="payment.tcAmount" :min="amountMinimum" id="PAP_tcAmount" :required="true" :disabled="true">
                Amount
              </app-number>
            </div>
            <div class="column is-4">
              <app-date-time v-model="payment.dReceived" size="small" id="PAY_dReceived" :disabled="true" :prepopulate="true">
                Received At
              </app-date-time>
            </div>
            <div class="column is-4 is-left">
              <InputCardType v-model="payment.lCreditCardTypeKey" :disabled="true" />
            </div>
            <div class="column is-8">
              <app-text v-model="payment.vc20PaymentInfo" maxlength="20" @change="maskPaymentInfo(payment)" :disabled="true" autocomplete="new-password">
                Check / Card Number
              </app-text>
            </div>
            <div class="column is-12 is-left">
              <app-customer v-model="payment.lCustomerKey">
                Customer
              </app-customer>
            </div>
            <div class="column is-8 is-left">
              <app-text v-model="payment.vc30CardholderName" maxlength="30" :disabled="true">
                Cardholder Name
              </app-text>
            </div>
            <div class="column is-4">
              <app-text v-model="payment.ch4ExpiryDate" maxlength="4" placeholder="MMYY" :disabled="true">
                Expiry
              </app-text>
            </div>
            <div class="column is-6 is-left">
              <app-text v-model="payment.vc20AuthorizationInfo" maxlength="20" :disabled="true">
                Authorization Number
              </app-text>
            </div>
            <div class="column is-6">
              <app-text v-model="payment.vc20TransactionTag" maxlength="20" :disabled="true">
                Transaction Number
              </app-text>
            </div>
            <div class="column is-6 is-left">
              <app-select v-model="payment.lReceiptTypeKey" :options="receiptTypes" keyAlias="Key" valueAlias="Value" :disabled="true">
                Receipt Type
              </app-select>
            </div>
            <div class="column is-6">
              <app-select v-model="payment.lReceivedBy" :options="employees" keyAlias="Key" valueAlias="Value" :disabled="true" :required="true">
                Received By
              </app-select>
            </div>
            <div class="column is-6 is-left" style="display: flex; align-items: flex-end;">
              <app-text v-model="payment.dReconciled" :disabled="true">
                Reconciled Date
              </app-text>
              <app-button @click="reconcilePayment(payment)" size="medium" :disabled="!canReconcile(payment)">
                <i class="far fa-clock"></i>
              </app-button>
            </div>
            <div class="column is-6">
              <app-data-point label="Reconciled By">
                {{ getUserName(payment.lReconciledBy) }}
              </app-data-point>
            </div>
            <div class="column is-12 is-left">
              <app-textarea v-model="payment.vc255Notes" maxlength="255" :disabled="true">
                Notes
              </app-textarea>
            </div>
          </div>
        </app-grid-form>
      </template>
    </app-accordian>

    <section class="_controls">
      <app-button id="payment.add" @click="addPayment" :disabled="true">
        Add Payment
      </app-button>

      <app-data-point label="Remaining Balance" v-if="shouldShowBalance">
        {{ balanceProxy | usd }}
      </app-data-point>
    </section>

    <span v-show="false">{{ updated_at }}</span>
  </span>
</template>

<script>
import numeral from 'numeral';
import Access from '@/utils/access';
import BaseSection from './BaseSection.vue';
import { mapGetters, mapActions } from 'vuex';
import RefundPayment from '../features/RefundPayment.vue';

import {
  VALUE_ID,
  CALL_SECTION_SALE_PAYMENTS
} from '@/config';

export default {
  // * When importing this component, the context may also require
  // * the following dependency: mixins/payment_mixin

  name: CALL_SECTION_SALE_PAYMENTS,

  extends: BaseSection,

  components: {
    RefundPayment
  },

  data () {
    return {
      sectionName: CALL_SECTION_SALE_PAYMENTS,

      users: [],
      employees: [],
      setFocusTo: '',
      paymentTypes: [],
      receiptTypes: [],
      updated_at: null
    };
  },

  computed: {
    ...mapGetters([
      '__state',
      'USER__state'
    ]),

    activePaymentTypes () {
      return this.paymentTypes.filter(paymentType => paymentType.Active);
    },

    paymentsProxy: {
      get () {
        return this.$_.get(this.call, 'SalePayments', []);
      },
      set (value) {
        this.$set(this.call, 'SalePayments', value);

        this.$nextTick(() => {
          this.$forceUpdate();
        });
      }
    },

    shouldShowBalance () {
      return [
        VALUE_ID.disposition.auctioned,
        VALUE_ID.disposition.scrapped,
        VALUE_ID.disposition.sold].includes(this.call.lFinalDispositionTypeKey);
    },

    balanceProxy () {
      const balance = this.paymentsProxy.reduce((balance, payment) => {
        if (this.$_.get(payment, 'alterBalance', false) && payment.tcAmount > 0) {
          return balance - payment.tcAmount;
        }
        return balance;
      }, this.call.tcBalance);

      this.$emit('setBalance', balance);

      return balance;
    },

    amountMinimum (payment) {
      return Access.has('payments.restrictNegativePayments') ? 0 : false;
    },

    reconciledByKeys () {
      return this.paymentsProxy.map(payment => {
        if (payment.lReconciledBy) { return payment.lReconciledBy; }
      });
    }
  },

  watch: {
    reconciledByKeys () {
      if (this.reconciledByKeys.length) {
        this.USER__getID({
          keys: this.reconciledByKeys,
          success: response => {
            this.users = response;
          }
        });
      }
    }
  },

  methods: {
    ...mapActions([
      'USER__getID',
      'TOPSCOMPANY__getEmployees',
      'PAYMENT__getPaymentTypes',
      'PAYMENT__getReceiptTypes',
      'TOPSCALL__getDefaultsForNewPayment'
    ]),

    getTypeValue (property, id) {
      let type = this.$_.find(this.paymentTypes, ['Key', Number(id)]);

      return this.$_.get(type, property, '');
    },

    canEditReceivedDate (payment) {
      if (!Access.has('payments.historicalReceivedDate')) return false;

      return this.canEditPayment(payment);
    },

    canEditPayment (payment) {
      if (this.isFreshPayment(payment)) return true;
      if (payment.dReconciled) return false;
      if (Access.has('payments.forceEdit')) return true;

      return Access.has('payments.edit') && this.$_.toString(this.__state.user.Key) === this.$_.toString(payment.lCreatedBy);
    },

    canDelete (payment) {
      if (this.isFreshPayment(payment)) return true;
      if (Access.has('payments.forceDelete')) return true;
      if (!Access.has('payments.delete')) return false;

      return Access.has('payments.edit');
    },

    isFreshPayment (payment) {
      return this.$_.has(payment, 'alterBalance');
    },

    getDefaultReceiptType () {
      const statusId = Number(this.$_.get(this.call, 'lCallStatusTypeKey', ''));

      switch (statusId) {
        case VALUE_ID.callStatus.dispatched:
          return VALUE_ID.paymentReceiptType.driver;

        case VALUE_ID.callStatus.inventory:
          return VALUE_ID.paymentReceiptType.lot;

        default:
          return this.call.lCallKey ? '' : VALUE_ID.paymentReceiptType.pending;
      }
    },

    getDefaultPaymentType () {
      return this.call.lCallKey ? '' : VALUE_ID.paymentType.creditCard;
    },

    async addPayment () {
      this.createIfMissing('SalePayments', []);

      let payment = {
        temporaryPaymentKey: this.$_.uniqueId(),
        alterBalance: true,
        lPaymentKey: '',
        lReceiptTypeKey: this.getDefaultReceiptType(),
        tcAmount: '',
        lPaymentTypeKey: this.getDefaultPaymentType(),
        vc20PaymentInfo: '',
        vc20AuthorizationInfo: '',
        dReceived: '',
        lReceivedBy: '',
        dReconciled: '',
        lReconciledBy: '',
        sReconciledBy: '',
        lCreatedBy: '',
        bTransferredToFES: false,
        bActive: false,
        dTransferredToFES: '',
        dDeposited: '',
        lDepositedBy: '',
        lProcessingTypeKey: '',
        vc30CardholderName: '',
        ch4ExpiryDate: '',
        dProcessed: '',
        vc20TransactionTag: '',
        vc20SequenceNum: '',
        lCreditCardTypeKey: '',
        vc255Notes: '',
        lCustomerKey: '',
        tcUnappliedAmount: '',
        lSubterminalKey: '',
        lCardStatusTypeKey: '',
        expandOnMount: true
      };

      this.$_.set(payment, 'focusHandle', 'control-' + payment.temporaryPaymentKey);

      if (this.call.lCallKey) {
        this.TOPSCALL__getDefaultsForNewPayment({
          callKey: this.call.lCallKey,
          success: async response => {
            payment = await this.applyDefaults(payment, response);

            this.paymentsProxy.push(payment);
            this.forceUpdate();
          }
        });
      } else {
        this.paymentsProxy.push(payment);
        this.forceUpdate();
      }

      this.setFocusTo = `.${payment.focusHandle}`;
      this.$emit('addSpeedBump', 'sale payments');
    },

    async applyDefaults (payment, defaults) {
      Object.keys(defaults).forEach(prop => {
        payment[prop] = defaults[prop];
      });

      return payment;
    },

    forceUpdate () {
      // Make Vue see recent change so that it is visible to the user.
      // Be sure to leave the updated_at property in the template.
      this.$set(this, 'updated_at', new Date());
    },

    removePayment (index) {
      this.paymentsProxy.splice(index, 1);
      this.forceUpdate();
    },

    afterCallRead () {
      this.applyFormatting();
    },

    applyFormatting () {
      this.$_.forEach(this.paymentsProxy, payment => {
        payment.tcAmount = numeral(payment.tcAmount).format('0.00');
      });
    },

    canRefundPayment (payment) {
      return payment.lProcessingTypeKey === VALUE_ID.creditCardProcessingType.towpay &&
        payment.tcAmount > 0 &&
        Access.has('payments.canRefund');
    },

    canReconcile (payment) {
      return Access.has('calls.reconcile') && this.canEditPayment(payment);
    },

    async reconcilePayment (payment) {
      payment.lReconciledBy = this.USER__state.Key;
      payment.dReconciled = await this.getNow();
    },

    getUserName (userKey) {
      let user = this.$_.find(this.users, ['Key', userKey]);

      return this.$_.get(user, 'Value', '');
    },

    maskPaymentInfo (payment) {
      if (payment.vc20PaymentInfo.length < 15) return;

      let digits = payment.vc20PaymentInfo.split('');
      let lastFour = '';

      digits.reverse();

      lastFour = digits.splice(0, 4);
      lastFour.reverse();

      payment.vc20PaymentInfo = '*'.repeat(digits.length) + lastFour.join('');
    }
  },

  mounted () {
    this.PAYMENT__getReceiptTypes({
      callback: response => {
        this.receiptTypes = response;
      }
    });

    this.PAYMENT__getPaymentTypes({
      callback: response => {
        this.paymentTypes = response;
      }
    });

    this.TOPSCOMPANY__getEmployees({
      callback: response => {
        this.employees = response;
      }
    });
  },

  updated () {
    this.$nextTick(() => {
      if (this.$_.isEmpty(this.setFocusTo)) return;

      let element = document.querySelector(this.setFocusTo);

      if (element) element.focus();

      this.setFocusTo = '';
    });
  }
};
</script>
