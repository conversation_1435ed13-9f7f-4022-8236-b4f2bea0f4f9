<template>
  <button class="service-item"
    :disabled="!canAddService({ service: internalService, dispatchKey })"
    @click="$emit('on-add-service', { service: internalService, dispatchKey })">

    <div class="name is-bold" :title="internalService.Description">
      {{ internalService.Description }}
    </div>
    <div>
      <div class="is-small is-upper is-bold opacity-50">Type</div>
      <div>{{ internalService.Type || '-' }}</div>
    </div>
    <div>
      <div class="is-small is-upper is-bold opacity-50">Unit</div>
      <div>{{ internalService.Units || '-' }}</div>
    </div>
    <div>
      <div class="is-small is-upper is-bold opacity-50">Class</div>
      <div>{{ internalService.TowClass || '-' }}</div>
    </div>
    <div>
      <div class="is-small is-upper is-bold opacity-50">GL Location</div>
      <div>{{ internalService.GLLocation || '-' }}</div>
    </div>
  </button>
</template>

<script>
import Expandable from '@/tower/liens/inputs/Expandable.vue';
import InputText from '@/tower/liens/inputs/Text.vue';
import InputNumber from '@/tower/liens/inputs/Number.vue';
import InputButton from '@/tower/liens/inputs/Button.vue';

export default {
  name: 'ServiceItem',

  components: {
    Expandable,
    InputText,
    InputNumber,
    InputButton
  },

  props: {
    service: {
      type: Object,
      required: true
    },
    call: {
      type: Object,
      required: true
    },
    dispatchKey: {
      type: [String, Number],
      required: true
    }
  },

  inject: [
    'isNewCall',
    'canAddService'
  ],

  data () {
    return {
      internalService: this.service
    };
  },

  methods: {
    async fetchPrice () {
      let service = {};

      const fetchForNewCall = () => {
        return new Promise(resolve => {
          this.$store.dispatch('TOPSCALL__getServicePricingForNew', {
            towTypeKey: this.call.lTowTypeKey,
            subterminalKey: this.call.lSubterminalKey,
            customerKey: this.call.lCustomerKey,
            serviceKey: this.internalService.ServiceKey,
            callback: response => { resolve(response); }
          });
        });
      };

      const fetchForExistingCall = () => {
        return new Promise(resolve => {
          this.$store.dispatch('TOPSCALL__getServicePricing', {
            callKey: this.call.lCallKey,
            orderType: 'T',
            serviceKey: this.internalService.ServiceKey,
            callback: response => { resolve(response); }
          });
        });
      };

      if (this.isNewCall()) {
        service = await fetchForNewCall();
      } else {
        service = await fetchForExistingCall();
      }

      return service;
    }
  },

  async mounted () {
    this.internalService = { ...this.internalService, ...(await this.fetchPrice()) };
  }
};
</script>

<style scoped>
.service-item {
  display: grid;
  grid-template-columns: 2fr repeat(4, 1fr);
  gap: 0.5rlh;

  padding: 0.5rlh 1rlh;
  width: calc(100dvw - 2rlh);
  max-width: 45rlh;
  text-align: left;
  color: var(--body-fg);
  background-color: white;
  border: 0;
  appearance: none;
  transition: opacity 0.2s linear;

  &[disabled] {
    opacity: 0.5;
  }

  .name {
    align-self: end;

    white-space: nowrap;
    overflow: hidden;
  }
}

.customize-item {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  align-items: start;
  gap: 0.5rlh;

  width: 100cqw;
  margin: 0 auto;

  .inputs {
    display: grid;
    gap: 0.25rlh;
  }
}
</style>
