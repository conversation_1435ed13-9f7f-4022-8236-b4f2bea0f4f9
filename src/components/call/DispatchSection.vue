<template>
  <span id="dispatch-section">
    <app-accordian class="_dispatch" v-for="(dispatch, index) in dispatchesProxy" :expand-on-mount="shouldExpandOnMount(index)" :key="dispatch.lDispatchKey">
      <div class="_thumbnail">
        <app-data-point label="Driver Number">{{ getProperty(dispatch.lDispatchKey, 'DriverNum') }}</app-data-point>
        <app-data-point label="Truck Number">{{ getProperty(dispatch.lDispatchKey, 'TruckNum') }}</app-data-point>
        <app-data-point label="Status">{{ getProperty(dispatch.lDispatchKey, 'Status') }}</app-data-point>
      </div>

      <template slot="body">
        <app-grid-form class="_editor" context="inline">
          <div class="columns is-multiline">
            <div class="column is-3">
              <app-shortcode id="DIS_lDriverKey" v-model="dispatch.lDriverKey" :options="drivers" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" :disabled="!canEditDriver(dispatch)">
                Driver Number
              </app-shortcode>
            </div>
            <div class="column is-3">
              <app-shortcode id="DIS_lTruckKey" v-model="dispatch.lTruckKey" :options="trucks" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" :disabled="!canEditProperty()">
                Truck Number
              </app-shortcode>
            </div>
            <div class="column is-3">
              <app-text v-model="dispatch.lTotalMileage" :disabled="!canEditProperty()">
                Mileage
              </app-text>
            </div>
            <div class="column is-3">
              <app-data-point class="_faux-input" label="Status">
                {{ getProperty(dispatch.lDispatchKey, 'Status') }}
              </app-data-point>
            </div>
            <div class="column is-6 is-left">
              <app-date-time v-model="dispatch.dAssigned" @change="backfill(dispatch, $event)" id="DIS_dAssigned" :disabled="!canEditProperty()">
                Assigned Date
              </app-date-time>
            </div>
            <div class="column is-6">
              <app-data-point class="_faux-input" label="By">
                {{ getProperty(dispatch.lDispatchKey, 'AssignedBy') }}
              </app-data-point>
            </div>
            <div class="column is-6 is-left">
              <app-date-time v-model="dispatch.dDispatched" @change="backfill(dispatch, $event)" id="DIS_dDispatched" :disabled="!canEditProperty()">
                Dispatched Date
              </app-date-time>
            </div>
            <div class="column is-6">
              <app-data-point class="_faux-input" label="By">
                {{ getProperty(dispatch.lDispatchKey, 'DispatchedBy') }}
              </app-data-point>
            </div>
            <div class="column is-6 is-left">
              <app-date-time v-model="dispatch.dAcknowledged" @change="backfill(dispatch, $event)" id="DIS_dAcknowledged" :disabled="!canEditProperty()">
                Acknowledged Date
              </app-date-time>
            </div>
            <div class="column is-3">
              <app-data-point class="_faux-input" label="By">
                {{ getProperty(dispatch.lDispatchKey, 'AcknowledgedBy') }}
              </app-data-point>
            </div>
            <div class="column is-3">
              <app-text v-model="dispatch.lOdometer_Acknowledged">
                Odometer
              </app-text>
            </div>
            <div class="column is-6 is-left">
              <app-date-time v-model="dispatch.dArrived" @change="backfill(dispatch, $event)" id="DIS_dArrived" :disabled="!canEditProperty()">
                Arrived Date
              </app-date-time>
            </div>
            <div class="column is-3">
              <app-data-point class="_faux-input" label="By">
                {{ getProperty(dispatch.lDispatchKey, 'ArrivedBy') }}
              </app-data-point>
            </div>
            <div class="column is-3">
              <app-text v-model="dispatch.lOdometer_Arrived">
                Odometer
              </app-text>
            </div>
            <div class="column is-6 is-left">
              <app-date-time v-model="dispatch.dHooked" @change="backfill(dispatch, $event)" id="DIS_dHooked" :disabled="!canEditProperty()">
                Hooked Date
              </app-date-time>
            </div>
            <div class="column is-3">
              <app-data-point class="_faux-input" label="By">
                {{ getProperty(dispatch.lDispatchKey, 'HookedBy') }}
              </app-data-point>
            </div>
            <div class="column is-3">
              <app-text v-model="dispatch.lOdometer_Hooked">
                Odometer
              </app-text>
            </div>
            <div class="column is-6 is-left">
              <app-date-time v-model="dispatch.dDropped" @change="backfill(dispatch, $event)" id="DIS_dDropped" :disabled="!canEditProperty()">
                Dropped Date
              </app-date-time>
            </div>
            <div class="column is-3">
              <app-data-point class="_faux-input" label="By">
                {{ getProperty(dispatch.lDispatchKey, 'DroppedBy') }}
              </app-data-point>
            </div>
            <div class="column is-3">
              <app-text v-model="dispatch.lOdometer_Dropped">
                Odometer
              </app-text>
            </div>
            <div class="column is-6 is-left">
              <app-date-time v-model="dispatch.dCompleted" @change="backfillAndOpenActions(dispatch, $event)" id="DIS_dCompleted" :disabled="!canEditProperty() || isNewCall">
                Completed Date
              </app-date-time>
            </div>
            <div class="column is-3">
              <app-data-point class="_faux-input" label="By">
                {{ getProperty(dispatch.lDispatchKey, 'CompletedBy') }}
              </app-data-point>
            </div>
            <div class="column is-3">
              <app-text v-model="dispatch.lOdometer_Completed">
                Odometer
              </app-text>
            </div>
            <div class="column is-6 is-left is-bottom">
              <app-text v-model="dispatch.tcCommission" v-if="canEditCommissionAmounts" :disabled="!canEditProperty()">
                Commissionable Amount
              </app-text>
            </div>
            <div class="column is-6 is-bottom">
              <app-text v-model="dispatch.vc50LoadNum" :disabled="true">
                Load Number
              </app-text>
            </div>
          </div>
        </app-grid-form>
      </template>
    </app-accordian>
  </span>
</template>

<script>
import tops from '@/utils/tops';
import Access from '@/utils/access.js';
import throttle from 'lodash/throttle';
import BaseSection from './BaseSection.vue';
import { createLeg } from './LegsSection/utils';
import { commonTransforms, applyTransformSchema } from '@/utils/responseTransformers.js';

import {
  VALUE_ID,
  CALL_SECTION_TOW_DISPATCH,
  EVENT_ALTER_CALL_COMPLETE,
  BEFORE_CALL_READ
} from '@/config.js';

export default {
  name: 'dispatch-section',

  extends: BaseSection,

  data () {
    return {
      sectionName: CALL_SECTION_TOW_DISPATCH,

      trucks: [],
      drivers: [],
      thumbnails: [],
      backfillableDates: [
        { order: 0, id: 'DIS_dAssigned', model: 'dAssigned' },
        { order: 1, id: 'DIS_dDispatched', model: 'dDispatched' },
        { order: 2, id: 'DIS_dAcknowledged', model: 'dAcknowledged' },
        { order: 3, id: 'DIS_dArrived', model: 'dArrived' },
        { order: 4, id: 'DIS_dHooked', model: 'dHooked' },
        { order: 5, id: 'DIS_dDropped', model: 'dDropped' },
        { order: 6, id: 'DIS_dCompleted', model: 'dCompleted' }
      ]
    };
  },

  computed: {
    canEditCommissionAmounts () {
      return Access.has('dispatches.editCommissionAmounts');
    },

    dispatchesProxy () {
      let dispatches = this.$_.get(this.call, 'Dispatches', []);

      return this.$_.filter(dispatches, ['bRetow', false]);
    },

    isFinalDispatch () {
      let totalDispatches = this.dispatchesProxy.length;
      let completedDispatches = this.$_.filter(this.dispatchesProxy, ['lDispatchStatusTypeKey', VALUE_ID.dispatchStatus.completed]).length;

      return (totalDispatches - completedDispatches) === 1;
    }
  },

  methods: {
    async getDispatchDetails() {
      const response = await tops.Call.GetDispatchDetails({
        CallKey: this.call.lCallKey
      });
      const transformedData = applyTransformSchema(response.data, commonTransforms.dispatchDetails);
      this.thumbnails = transformedData.Data;
    },

    afterCallRead () {
      this.$nextTick(() => {
        this.getDispatchDetails();
      });
    },

    getProperty (dispatchKey, property) {
      let dispatch = this.$_.find(this.thumbnails, ['Key', dispatchKey]);

      if (!dispatch) return;

      return this.$_.get(dispatch, property, '');
    },

    async getDrivers() {
      const response = await tops.TOPSCompany.GetDrivers();
      this.drivers = response.data.Data;
    },

    async getTrucks() {
      const response = await tops.TOPSCompany.GetTrucks();
      this.trucks = response.data.Data;
    },

    backfill (dispatch, input) {
      const watermark = this.backfillableDates.find(date => date.id === input.id);
      const datesBelowWatermark = this.backfillableDates.filter(date => date.order < watermark.order);

      datesBelowWatermark.forEach(date => {
        if (!dispatch[date.model]) {
          dispatch[date.model] = input.value;
        }
      });
    },

    shouldTriggerCompletionProcess (dispatch) {
      return this.isFinalDispatch && !!dispatch.dCompleted;
    },

    async backfillAndOpenActions (dispatch, input) {
      this.backfill(dispatch, input);

      if (!this.shouldTriggerCompletionProcess(dispatch)) return;

      document.querySelector('#DIS_dCompleted').blur();

      this.$hub.$emit(EVENT_ALTER_CALL_COMPLETE, {
        dispatch,
        mutations: [
          {
            action: `DispatchUpdate_${dispatch.lDispatchKey}`,
            item: 'Assigned',
            value: dispatch.dAssigned
          },
          {
            action: `DispatchUpdate_${dispatch.lDispatchKey}`,
            item: 'Dispatched',
            value: dispatch.dDispatched
          },
          {
            action: `DispatchUpdate_${dispatch.lDispatchKey}`,
            item: 'Acknowledged',
            value: dispatch.dAcknowledged
          },
          {
            action: `DispatchUpdate_${dispatch.lDispatchKey}`,
            item: 'Arrived',
            value: dispatch.dArrived
          },
          {
            action: `DispatchUpdate_${dispatch.lDispatchKey}`,
            item: 'Hooked',
            value: dispatch.dHooked
          },
          {
            action: `DispatchUpdate_${dispatch.lDispatchKey}`,
            item: 'Dropped',
            value: dispatch.dDropped
          },
          {
            action: `DispatchUpdate_${dispatch.lDispatchKey}`,
            item: 'Completed',
            value: dispatch.dCompleted
          }
        ]
      });
    },

    canEditDriver (dispatch) {
      if (this.isNewCall) return true;
      if (!dispatch.bRetow && this.call.lCallStatusTypeKey === VALUE_ID.callStatus.dispatched) return true;
      if (dispatch.bRetow && this.call.lCallStatusTypeKey === VALUE_ID.callStatus.retowDispatch) return true;

      return false;
    },

    shouldExpandOnMount (index) {
      return this.dispatchesProxy.length === 1 && index === 0;
    }
  },

  mounted () {
    this.getDrivers();
    this.getTrucks();
  }
};
</script>
