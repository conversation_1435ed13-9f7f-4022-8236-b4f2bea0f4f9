<template>
  <app-shortcode
    v-model.number="$value"
    :options="towTypes"
    :all-options="towTypeUtils.allTowTypes()"
    :disabled="!canEdit"
    :required="required"
    :uuid="uuid">
    Tow Type
  </app-shortcode>
</template>

<script>
import Control from '@/components/ancestors/Control.vue';
import useTowType from './useTowType.js';

export default {
  name: 'InputTowType',

  extends: Control,

  props: {
    value: {
      type: [Number, String, null],
      required: true
    }
  },

  data () {
    const towTypeUtils = useTowType({ loadOnMount: true });

    return {
      uuid: this.$_.uniqueId('towtype-'),
      towTypeUtils
    };
  },

  computed: {
    $value: {
      get () {
        return this.value;
      },
      set (value) {
        const _value = [null, undefined].includes(value) ? '' : value;
        this.$emit('input', _value);
      }
    },

    towTypes () {
      if (this.context === 'search') {
        return this.towTypeUtils.allTowTypes();
      } else {
        return this.towTypeUtils.towTypes();
      }
    },

    sharedState () {
      return {
        isLoading: this.towTypeUtils.isLoading(),
        isLoaded: this.towTypeUtils.isLoaded()
      };
    },

    canEdit () {
      if (this.context === 'search') return true;
      if (this.context === 'active') return true;

      const selectedOption = this.towTypeUtils.allTowTypes().find(option => option.Key === Number(this.value));
      if (selectedOption && Number(selectedOption.Key) > 0) {
        return selectedOption.Active && !this.disabled;
      }

      return !this.disabled;
    }
  }
};
</script>
