import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import InputTowType from '../Index.vue';

// Mock the useTowType composable
vi.mock('../useTowType.js', () => ({
  default: vi.fn(() => ({
    towTypes: vi.fn().mockReturnValue([]),
    allTowTypes: vi.fn().mockReturnValue([]),
    isLoading: vi.fn().mockReturnValue(false),
    isLoaded: vi.fn().mockReturnValue(true),
    loadTowTypes: vi.fn(),
    isDropoffTowType: vi.fn()
  }))
}));

// Import the mocked composable to manipulate it in tests
import useTowType from '../useTowType.js';

// Mock the Control component that InputTowType extends
vi.mock('@/components/ancestors/Control.vue', () => ({
  default: {
    name: 'Control',
    props: {
      value: {
        type: [Number, String, null],
        required: true
      },
      disabled: Boolean,
      required: Boolean,
      context: String
    }
  }
}));

// Mock the app-shortcode component
const AppShortcode = {
  name: 'app-shortcode',
  template: '<div><slot></slot></div>',
  props: ['value', 'options', 'allOptions', 'disabled', 'required', 'uuid']
};

describe('InputTowType.vue', () => {
  let wrapper;
  let mockTowTypeUtils;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Create mock tow type data
    const mockActiveTowTypes = [
      { Key: 1, Value: 'Standard', Active: true },
      { Key: 2, Value: 'Flatbed', Active: true }
    ];

    const mockAllTowTypes = [
      ...mockActiveTowTypes,
      { Key: 3, Value: 'Dropoff', Active: false }
    ];

    // Set up the mock implementation for useTowType
    mockTowTypeUtils = {
      towTypes: vi.fn().mockReturnValue(mockActiveTowTypes),
      allTowTypes: vi.fn().mockReturnValue(mockAllTowTypes),
      isLoading: vi.fn().mockReturnValue(false),
      isLoaded: vi.fn().mockReturnValue(true),
      loadTowTypes: vi.fn(),
      isDropoffTowType: vi.fn()
    };

    useTowType.mockReturnValue(mockTowTypeUtils);

    // Mock lodash uniqueId
    const mockLodash = {
      uniqueId: vi.fn().mockReturnValue('towtype-123')
    };

    // Mount the component
    wrapper = mount(InputTowType, {
      propsData: {
        value: 1,
        disabled: false,
        required: true
      },
      stubs: {
        'app-shortcode': AppShortcode
      },
      mocks: {
        $_: mockLodash
      }
    });
  });

  it('should render correctly', () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.text()).toContain('Tow Type');
  });

  it('should initialize with useTowType composable', () => {
    expect(useTowType).toHaveBeenCalledWith({ loadOnMount: true });
    expect(wrapper.vm.towTypeUtils).toBeDefined();
  });

  it('should return active tow types by default', () => {
    expect(wrapper.vm.towTypes).toEqual(mockTowTypeUtils.towTypes());
  });

  it('should return all tow types when context is search', async () => {
    await wrapper.setProps({ context: 'search' });
    expect(mockTowTypeUtils.allTowTypes).toHaveBeenCalled();
  });

  it('should emit input event when value changes', async () => {
    const appShortcode = wrapper.findComponent({ name: 'app-shortcode' });
    await appShortcode.vm.$emit('input', 2);

    expect(wrapper.emitted().input).toBeTruthy();
    expect(wrapper.emitted().input[0]).toEqual([2]);
  });

  it('should convert null/undefined values to empty string', async () => {
    const appShortcode = wrapper.findComponent({ name: 'app-shortcode' });
    await appShortcode.vm.$emit('input', null);

    expect(wrapper.emitted().input[0]).toEqual(['']);
  });

  it('should allow editing in search context', async () => {
    await wrapper.setProps({ context: 'search' });
    expect(wrapper.vm.canEdit).toBe(true);
  });

  it('should allow editing in active context', async () => {
    await wrapper.setProps({ context: 'active' });
    expect(wrapper.vm.canEdit).toBe(true);
  });

  it('should check if selected option is active for canEdit', async () => {
    // Set up a mock implementation for allTowTypes that returns a specific option
    mockTowTypeUtils.allTowTypes.mockReturnValue([
      { Key: 1, Value: 'Standard', Active: true },
      { Key: 2, Value: 'Flatbed', Active: false }
    ]);

    // Test with active option
    await wrapper.setProps({ value: 1, context: 'edit' });
    expect(wrapper.vm.canEdit).toBe(true);

    // Test with inactive option
    await wrapper.setProps({ value: 2, context: 'edit' });
    expect(wrapper.vm.canEdit).toBe(false);
  });

  it('should respect disabled prop', async () => {
    await wrapper.setProps({ disabled: true, context: 'edit' });
    expect(wrapper.vm.canEdit).toBe(false);
  });

  it('should pass allOptions to app-shortcode component', () => {
    const appShortcode = wrapper.findComponent({ name: 'app-shortcode' });
    expect(appShortcode.props('allOptions')).toEqual(mockTowTypeUtils.allTowTypes());
    expect(mockTowTypeUtils.allTowTypes).toHaveBeenCalled();
  });

  it('should only show active tow types in normal context (not search)', () => {
    // Ensure we're not in search context
    expect(wrapper.vm.context).not.toBe('search');

    // Should return only active tow types
    expect(wrapper.vm.towTypes).toEqual(mockTowTypeUtils.towTypes());
    expect(mockTowTypeUtils.towTypes).toHaveBeenCalled();

    // Verify that inactive tow types are not included
    const activeTowTypes = mockTowTypeUtils.towTypes();
    const allTowTypes = mockTowTypeUtils.allTowTypes();

    expect(activeTowTypes.length).toBeLessThan(allTowTypes.length);
    expect(activeTowTypes.every(towType => towType.Active)).toBe(true);
  });

  it('should prevent selection of inactive tow types like Internet', () => {
    // Set up mock data with Internet tow type (inactive)
    const mockAllTowTypesWithInternet = [
      { Key: 1, Value: 'Standard', Active: true },
      { Key: 2, Value: 'Flatbed', Active: true },
      { Key: 0, Value: 'Internet', Active: false }
    ];

    const mockActiveTowTypesOnly = [
      { Key: 1, Value: 'Standard', Active: true },
      { Key: 2, Value: 'Flatbed', Active: true }
    ];

    mockTowTypeUtils.allTowTypes.mockReturnValue(mockAllTowTypesWithInternet);
    mockTowTypeUtils.towTypes.mockReturnValue(mockActiveTowTypesOnly);

    // In normal context, should not include Internet
    expect(wrapper.vm.towTypes).toEqual(mockActiveTowTypesOnly);
    expect(wrapper.vm.towTypes.find(t => t.Value === 'Internet')).toBeUndefined();
  });
});
