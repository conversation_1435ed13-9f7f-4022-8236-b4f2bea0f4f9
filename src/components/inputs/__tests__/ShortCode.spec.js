import { mount } from '@vue/test-utils';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import ShortCode from '../ShortCode.vue';

// Mock lodash
const mockLodash = {
  toString: vi.fn((val) => String(val)),
  set: vi.fn((obj, path, value) => {
    obj[path] = value;
    return obj;
  })
};

describe('ShortCode.vue', () => {
  let wrapper;

  const mockOptions = [
    { Key: 1, Value: 'Active Option 1', ShortCode: 'ACT1', Active: true },
    { Key: 2, Value: 'Active Option 2', ShortCode: 'ACT2', Active: true }
  ];

  const mockAllOptions = [
    ...mockOptions,
    { Key: 0, Value: 'Internet', ShortCode: 'INT', Active: false },
    { Key: 3, Value: 'Inactive Option', ShortCode: 'INACT', Active: false }
  ];

  beforeEach(() => {
    wrapper = mount(ShortCode, {
      propsData: {
        options: mockOptions,
        allOptions: mockAllOptions,
        value: 1
      },
      mocks: {
        $_: mockLodash,
        $awaitNextTicks: vi.fn(),
        $gsap: {
          to: vi.fn()
        }
      }
    });
  });

  it('should display active option value correctly', async () => {
    await wrapper.setProps({ value: 1 });
    expect(wrapper.vm.valueReadonly).toBe('Active Option 1');
  });

  it('should display inactive option value from allOptions when not found in options', async () => {
    await wrapper.setProps({ value: 0 });
    expect(wrapper.vm.valueReadonly).toBe('Internet');
  });

  it('should display inactive option value from allOptions', async () => {
    await wrapper.setProps({ value: 3 });
    expect(wrapper.vm.valueReadonly).toBe('Inactive Option');
  });

  it('should fallback to raw value when option not found in either array', async () => {
    await wrapper.setProps({ value: 999 });
    expect(wrapper.vm.valueReadonly).toBe(999);
  });

  it('should work without allOptions prop', () => {
    wrapper = mount(ShortCode, {
      propsData: {
        options: mockOptions,
        value: 1
      },
      mocks: {
        $_: mockLodash,
        $awaitNextTicks: vi.fn(),
        $gsap: {
          to: vi.fn()
        }
      }
    });

    expect(wrapper.vm.valueReadonly).toBe('Active Option 1');
  });

  it('should fallback to raw value when allOptions is null and option not found', () => {
    wrapper = mount(ShortCode, {
      propsData: {
        options: mockOptions,
        allOptions: null,
        value: 0
      },
      mocks: {
        $_: mockLodash,
        $awaitNextTicks: vi.fn(),
        $gsap: {
          to: vi.fn()
        }
      }
    });

    expect(wrapper.vm.valueReadonly).toBe(0);
  });

  it('should handle the specific Tow Type Internet scenario', async () => {
    // Simulate the real scenario: active options only include active tow types
    const activeTowTypes = [
      { Key: 1, Value: 'Standard', ShortCode: 'STD', Active: true },
      { Key: 2, Value: 'Flatbed', ShortCode: 'FLAT', Active: true }
    ];

    // All options include inactive ones like Internet
    const allTowTypes = [
      ...activeTowTypes,
      { Key: 0, Value: 'Internet', ShortCode: 'INT', Active: false }
    ];

    wrapper = mount(ShortCode, {
      propsData: {
        options: activeTowTypes, // Only active options are passed normally
        allOptions: allTowTypes, // All options available as fallback
        value: 0 // Internet tow type
      },
      mocks: {
        $_: mockLodash,
        $awaitNextTicks: vi.fn(),
        $gsap: {
          to: vi.fn()
        }
      }
    });

    // Should display "Internet" instead of "0"
    expect(wrapper.vm.valueReadonly).toBe('Internet');
  });
});
