<template>
<p class="control" ref="container">
  <label class="app-shortcode"
    @click="showModal">

    <div class="-label">
      <slot></slot> <span v-if="required" class="-required"><i class="fas fa-circle-small"></i></span>
    </div>

    <input
      ref="control"
      type="text"
      :class="inputClasses"
      autocomplete="off"
      v-model="valueReadonly"
      :id="id"
      :required="required"
      :readonly="true"
      :disabled="disabled"
      :placeholder="placeholder"
      :tabindex="tabindex"
      @focus.once="showModal({ onlyOnEmpty: true })"
      @keyup.enter="showModal"
      @keyup.esc="onEscape"
      @keyup.down="showModal"
      @click="emit('click')"
      @blur="emit('blur')" />

    <dialog class="search-dialog" ref="searchDialog"
      @cancel.prevent
      @click.prevent.stop="onClickOutside">

      <input class="term"
        type="search"
        v-model="searchTerm"
        placeholder="Search"
        autofocus
        @keydown.up="changeIntent('back')"
        @keydown.down="changeIntent('next')"
        @keyup.enter="selectOption(valueIntent)"
        @keydown.tab.self="selectOption(valueIntent)"
        @keyup.esc="onEscape">

      <div class="options">
        <label class="option"
          v-for="option in optionsProxy"
          :data-active="option[keyAlias] === valueProxy"
          :key="option[keyAlias]"
          @click.stop.prevent="selectOption(option[keyAlias])">
          <div class="value" v-html="option.markedValue || option[valueAlias]"></div>
          <div class="short-code is-small is-bold" v-html="option.markedShortCode || option[shortCodeAlias]"></div>
          <input type="radio" v-model="valueIntent" :name="`${id}_intent`" :value="option[keyAlias]">
        </label>
        <label class="option" v-if="optionsProxy.length === 0" key="no-results">
          <div class="value">No results</div>
        </label>
      </div>

    </dialog>

  </label>
</p>
</template>

<script>
import Fuse from 'fuse.js';
import Control from '@/components/ancestors/Control.vue';

export default {
  name: 'app-shortcode',

  extends: Control,

  props: {
    options: { type: Array, required: true },
    allOptions: { type: Array, required: false, default: null },
    keyAlias: { type: [String, Number], required: false, default: 'Key' },
    valueAlias: { type: [String, Number], required: false, default: 'Value' },
    activeAlias: { type: [String, Number], required: false, default: 'Active' },
    shortCodeAlias: { type: [String, Number], required: false, default: 'ShortCode' }
  },

  data () {
    return {
      searchTerm: '',

      valueIntent: null,
      optionsFuse: null,
      isSearchContext: this.context === 'search',
      inputClasses: {
        'input': true
      }
    };
  },

  computed: {
    valueProxy: {
      get () {
        return this.value;
      },
      set (value = '') {
        this.oldValue = this.$_.toString(this.valueProxy);
        this.$emit('input', this.formatValue(value));

        this.$nextTick(() => {
          this.emit('change');
        });
      }
    },

    valueReadonly () {
      let option = this.options.find(option => this.$_.toString(option[this.keyAlias]) === this.$_.toString(this.valueProxy));

      if (!option && this.allOptions) {
        option = this.allOptions.find(option => this.$_.toString(option[this.keyAlias]) === this.$_.toString(this.valueProxy));
      }

      return option ? option[this.valueAlias] : this.valueProxy;
    },

    optionsProxy () {
      if (this.searchTerm) {
        const options = this.optionsFuse.search(this.searchTerm).map(option => option.item);

        this.valueIntent = options[0] ? options[0][this.keyAlias] : null;

        return options.map(({ markedValue, markedShortCode, ...option }) => {
          option.markedValue = option[this.valueAlias].replace(new RegExp(this.searchTerm, 'gi'), '<mark>$&</mark>');
          option.markedShortCode = option[this.shortCodeAlias].replace(new RegExp(this.searchTerm, 'gi'), '<mark>$&</mark>');
          return option;
        });
      }

      let searchContextOptions = this.isSearchContext ? this.getSearchContextOptions() : [];

      return [...searchContextOptions, ...this.options];
    }
  },

  watch: {
    options: {
      handler () {
        this.optionsFuse = new Fuse(this.options, {
          keys: [
            { name: this.shortCodeAlias, weight: 1.0 },
            { name: this.valueAlias, weight: 0.5 }
          ],
          threshold: 0.3,
          ignoreLocation: false
        });
      },
      immediate: true
    }
  },

  methods: {
    getSearchContextOptions () {
      let emptyOption = {};
      this.$_.set(emptyOption, this.keyAlias, 'Empty');
      this.$_.set(emptyOption, this.valueAlias, 'Empty');
      this.$_.set(emptyOption, this.shortCodeAlias, 'EMPTY');

      let notEmptyOption = {};
      this.$_.set(notEmptyOption, this.keyAlias, 'Not Empty');
      this.$_.set(notEmptyOption, this.valueAlias, 'Not Empty');
      this.$_.set(notEmptyOption, this.shortCodeAlias, 'NOTEMPTY');

      return [emptyOption, notEmptyOption];
    },

    async showModal ({ onlyOnEmpty = false }) {
      if (onlyOnEmpty && this.valueProxy) return;
      if (this.disabled) return;

      await this.setDialogPosition();
      this.$refs.searchDialog.showModal();
    },

    selectOption (key) {
      /**
       * @event change
       * Compatability layer for PriceGuard to intercept the change event
       */
      const event = new window.CustomEvent('change', { bubbles: true });
      const element = document.getElementById(this.id);
      element.dispatchEvent(event);
      // End compatability layer

      this.valueProxy = key;
      this.hideModal();
    },

    async hideModal () {
      this.$refs.searchDialog.close();
    },

    onEscape () {
      if (!this.$refs.searchDialog.open) {
        this.valueProxy = null;
      } else {
        this.hideModal();
      }
    },

    onClickOutside (event) {
      const dialog = this.$refs.searchDialog.getBoundingClientRect();

      if (
        event.clientX < dialog.left ||
        event.clientX > dialog.right ||
        event.clientY < dialog.top ||
        event.clientY > dialog.bottom
        ) {
        this.hideModal();
      }
    },

    async changeIntent (direction) {
      let intentIndex = this.optionsProxy.findIndex(option => option[this.keyAlias] === this.valueIntent);

      if (direction === 'next') {
        intentIndex++;
      } else {
        intentIndex--;
      }

      if (intentIndex < 0) {
        intentIndex = this.optionsProxy.length - 1;
      } else if (intentIndex >= this.optionsProxy.length) {
        intentIndex = 0;
      }

      const nextOption = this.optionsProxy[intentIndex];

      this.valueIntent = nextOption ? nextOption[this.keyAlias] : this.optionsProxy[0][this.keyAlias];

      await this.$awaitNextTicks(1);

      const intentElement = this.$refs.searchDialog.querySelector('input[type="radio"]:checked');

      if (intentElement) {
        this.$gsap.to(this.$refs.searchDialog, {
          duration: 0.4,
          ease: 'power4.out',
          scrollTo: {
            y: intentElement,
            offsetY: 50
          }
        });
      }
    },

    setDialogPosition () {
      return new Promise(resolve => {
        const container = this.$refs.container;
        const containerRect = container.getBoundingClientRect();
        const dialogTop = containerRect.y + containerRect.height;

        this.$refs.searchDialog.style.setProperty('--dialog-top', `${dialogTop}px`);
        this.$refs.searchDialog.style.setProperty('--dialog-left', `${containerRect.left}px`);
        this.$refs.searchDialog.style.setProperty('--dialog-width', `${containerRect.width}px`);

        resolve();
      });
    }
  }
};
</script>

<style>
.control {
  position: relative;
}

.app-shortcode {
  position: relative;
}

.search-dialog {
  --dialog-top: 0;
  --dialog-left: 0;
  --dialog-width: 400px;

  position: absolute;
  top: var(--dialog-top);
  left: var(--dialog-left);

  margin: 0;
  padding: 0;
  min-width: 25ch;
  width: var(--dialog-width);
  max-height: calc(100vh - var(--dialog-top) - 1rem);
  border-radius: 0.5rem;
  overflow-x: hidden;
  overflow-y: auto;
  outline: none;

  .term {
    position: sticky;
    top: 0;

    margin-bottom: 0.5rem;
    padding: 0.5rem 1rem;
    background: hsl(0, 0%, 100%);
    border: none;
    border-bottom: 1px solid hsla(0, 0%, 0%, 0.1);
    z-index: 10;
  }

  .option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 0.5rem;

    padding: 0.5rem 1rem;

    &[data-active="true"] {
      background-color: var(--input-bg-focus);
    }

    & input {
      position: absolute;
      visibility: hidden;
    }

    & .short-code {
      color: hsla(var(--pure-blue-h), var(--pure-blue-s), var(--pure-blue-l), 1);
    }

    &:has(input[type="radio"]:checked) {
      background-color: var(--selected-background);
    }
  }

  & mark {
    position: relative;

    background-color: transparent;

    &::before {
      --padding: -0.1rem;

      position: absolute;
      top: var(--padding);
      right: calc(var(--padding) * 3);
      bottom: var(--padding);
      left: calc(var(--padding) * 3);

      content: "";
      background-color: hsla(var(--pure-yellow-h), var(--pure-yellow-s), var(--pure-yellow-l), 0.3);
      mix-blend-mode: multiply;
      border-radius: 1rem;
    }
  }
}
</style>
