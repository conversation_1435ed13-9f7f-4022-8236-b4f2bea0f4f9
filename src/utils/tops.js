import axios from 'axios';
import { get } from 'lodash';
import store from '@/store';
import { VERSION } from '@/config';
import { storifyDate } from '@/utils/filters';

class ApiClient {
  constructor() {
    this.apiUrl = import.meta.env.VITE_TXI_API;
    this.debug = store?.getters.__state.appMode === 'DEBUG';
    this.timeout = 60 * 1000;
  }

  /**
   * Make a request to the TOPS API
   */
  async request(noun, verb, data = {}, options = {}) {
    // Build request parameters
    const parameters = this._buildParameters(noun, verb, data);

    // Log request in debug mode
    // if (this.debug) {
    //   console.log(`%crequest → %c${noun}, ${verb})`,
    //     'font-variant: small-caps; color: #0074D9',
    //     'font-weight: bold',
    //     'color: #AAAAAA',
    //     parameters);
    // }

    try {
      // Make the request
      const response = await axios({
        method: 'post',
        headers: {
          'X-txi-api': `App:TOPS Browser,Version:${VERSION},Noun:${noun},Verb:${verb}`
        },
        url: this.apiUrl,
        data: parameters,
        timeout: this.timeout
      });

      // Log response in debug mode
      // if (this.debug) {
      //   console.log(`%cresponse → %c${noun}, ${verb})`,
      //     'font-variant: small-caps; color: #39CCCC',
      //     'font-weight: bold',
      //     'color: #AAAAAA',
      //     response);
      // }

      return response;
    } catch (error) {
      // Log error in debug mode
      if (this.debug) {
        console.log(`%cerror → %c${noun}, ${verb})`,
          'font-variant: small-caps; color: #FF4136',
          'font-weight: bold',
          'color: #AAAAAA',
          error);
      }

      throw error;
    }
  }

  /**
   * Build request parameters
   */
  _buildParameters(noun, verb, data) {
    const rootState = store.state;

    return {
      Operation: {
        Noun: noun,
        Verb: verb,
        ProductKey: rootState.product.key,
        OrgUnitKey: get(rootState, 'orgUnitKey'),
        Mode: get(rootState, 'appMode'),
        ResponseData: 'JSON',
        LastRead: storifyDate('') // @TODO
      },
      Authentication: {
        UserKey: rootState.user.Key,
        InstanceKey: rootState.instance.Key,
        AuthenticationKey: rootState.instance.Authentication
      },
      Data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        },
        ...data
      }
    };
  }
}

// Create a singleton API client
const apiClient = new ApiClient();

/**
 * Create a noun-specific API namespace
 */
function createNounApi(noun) {
  return new Proxy({}, {
    get(_, verb) {
      // Return a function that makes the API request
      return async (data = {}, options = {}) => {
        return apiClient.request(noun, verb, data, options);
      };
    }
  });
}

/**
 * Main export
 */
const tops = new Proxy({}, {
  get(target, noun) {
    // Create and cache the noun API if it doesn't exist
    if (!target[noun]) {
      target[noun] = createNounApi(noun);
    }
    return target[noun];
  }
});

export default tops;
